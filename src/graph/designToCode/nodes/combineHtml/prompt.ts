// HTML 合并系统提示
export function getCombineHtmlSystemPrompt(): string {
  return `你是一个专业的 HTML 合并专家。你的任务是将多个 HTML 片段智能合并成一个完整的、结构良好的 HTML 文档。

合并要求：
1. 保持每个 HTML 片段的原始结构和样式
2. 确保合并后的 HTML 语法正确
3. 移除重复的样式和脚本
4. 优化 HTML 结构，使其更加清晰
5. 保持响应式设计特性
6. 确保合并后的代码可以直接在浏览器中运行
7. 保持语义化的 HTML 标签
8. 优化 CSS 样式，避免冲突
9. 确保合并后的页面具有良好的用户体验

请只返回合并后的 HTML 代码，不要包含任何解释或注释。`;
}

// HTML 合并用户提示模板
export function getCombineHtmlUserPrompt(
  htmlFragments: string[],
  fragmentCount: number
): string {
  return `请将以下 ${fragmentCount} 个 HTML 片段智能合并成一个完整的 HTML 文档：

${htmlFragments.join("\n\n")}

请确保合并后的 HTML 是完整、正确且可以直接使用的。合并时请注意：
- 保持每个片段的原始功能和样式
- 优化整体结构，使其更加清晰
- 确保所有样式和脚本正确工作
- 保持响应式设计特性`;
}

// 错误处理提示
export function getCombineHtmlErrorPrompt(error: string): string {
  return `HTML 合并过程中出现错误：${error}

请检查：
1. HTML 片段是否完整
2. 是否有语法错误
3. 是否有冲突的样式或脚本
4. 是否有缺失的依赖

请提供修复建议或重新尝试合并。`;
}

// 验证提示
export function getCombineHtmlValidationPrompt(htmlResults: any[]): string {
  const validCount = htmlResults.filter((r) => r.status === "completed").length;
  const totalCount = htmlResults.length;

  return `验证 HTML 合并输入：
- 总片段数：${totalCount}
- 有效片段数：${validCount}
- 失败片段数：${totalCount - validCount}

请确认是否继续合并，或者需要处理失败的片段。`;
}
