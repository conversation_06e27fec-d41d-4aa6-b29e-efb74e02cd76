import { StateGraph, MemorySaver, END, START } from "@langchain/langgraph";
import {
  BaseMessage,
  SystemMessage,
  HumanMessage,
} from "@langchain/core/messages";
import { CombineHtmlState, HtmlResultItem } from "./types.js";
import { createModel } from "../../model/index.js";
import { getCombineHtmlSystemPrompt, getCombineHtmlUserPrompt } from "./prompt.js";
import { formatHtmlFragmentsForLLM } from "./utils.js";

const checkpointer = new MemorySaver();

// 状态对象定义
const CombineHtmlStateObj = {
  input: {
    value: (x: HtmlResultItem[], y: HtmlResultItem[]) => y,
    default: () => [],
  },
  output: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  error: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
};

/**
 * 验证输入条件边函数 - 检查输入数据的有效性
 */
function validateInput(state: CombineHtmlState): "combineHtml" {
  console.log("CombineHtml: 验证输入...");

  if (!state.input || !Array.isArray(state.input) || state.input.length === 0) {
    console.log("CombineHtml: 输入验证失败 - 没有提供有效的 HTML 结果");
    state.error = "输入验证失败：没有提供有效的 HTML 结果";
    throw new Error(state.error);
  }

  console.log(
    `CombineHtml: 输入验证通过 - 有 ${state.input.length} 个 HTML 结果`
  );
  return "combineHtml";
}

/**
 * 合并 HTML 节点 - 使用 LLM 智能合并
 */
async function combineHtmlNode(state: CombineHtmlState) {
  console.log("CombineHtml: 开始使用 LLM 合并 HTML...");

  // 创建 LLM 模型
  const model = createModel();

  // 使用提示模板
  const systemPrompt = getCombineHtmlSystemPrompt();
  const htmlFragments = formatHtmlFragmentsForLLM(state.input);
  const userPrompt = getCombineHtmlUserPrompt(
    htmlFragments,
    state.input.length
  );

  console.log(`CombineHtml: 调用 LLM 合并 ${state.input.length} 个 HTML 片段`);

  // 调用 LLM
  const response = await model.invoke([
    new SystemMessage(systemPrompt),
    new HumanMessage(userPrompt),
  ]);

  const combinedHtml = response.content as string;

  console.log("CombineHtml: LLM 合并完成");

  return {
    output: combinedHtml,
    messages: [
      new SystemMessage(`LLM 合并完成，合并了 ${state.input.length} 个结果`),
    ],
  };
}

// 构建 CombineHtml 工作流图
const workflow = new StateGraph<CombineHtmlState>({
  channels: CombineHtmlStateObj,
})
  .addNode("combineHtml", combineHtmlNode)
  .addConditionalEdges(START, validateInput, {
    combineHtml: "combineHtml",
  })
  .addEdge("combineHtml", END);

// 编译 CombineHtml 工作流
export const graph = workflow.compile({ checkpointer });
graph.name = "combineHtml";

// 导出类型
export type { CombineHtmlState, HtmlResultItem };
