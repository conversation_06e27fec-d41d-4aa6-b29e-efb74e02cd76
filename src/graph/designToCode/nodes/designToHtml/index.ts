import { StateGraph, MemorySaver, END, START } from "@langchain/langgraph";
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
  HumanMessage,
} from "@langchain/core/messages";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { createModel } from "../../model/index.js";
import { writeFileTool } from "../../../../tools/writeFiles.js";
import {
  getHtmlToCodeSystemPrompt,
  getImgToCodeSystemPrompt,
  getRoundControlPrompt,
  getHtmlRefactorPrompt,
  getReviewCodeSystemPrompt,
} from "./prompt/index.js";
import { isHtml, isImg, printLog } from "./utils.js";
import { DesignToHtmlState } from "./types.js";
import { DesignItem } from "../../types.js";

// 常量定义
const MAX_REVIEW_ROUNDS = 1; // 最大审查轮次

const tools = [writeFileTool];

const checkpointer = new MemorySaver();

// The state for our graph will be a list of messages

const DesignToHtmlStateObj = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  reviewRound: {
    value: (x: number, y: number) => y, // 使用最新的轮次值
    default: () => 0,
  },
  input: {
    value: (x: DesignItem, y: DesignItem) => y, // 使用最新的输入值
    default: () => ({ pageName: "", pageContent: "", type: "html" as const }),
  },
  output: {
    value: (x: string, y: string) => y, // 使用最新的输出值
    default: () => "",
  },
  error: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
};

// 判断输入内容类型的函数
function routeByContentType(
  state: DesignToHtmlState
): "htmlToCode" | "imgToCode" {
  // 优先使用 type 字段进行路由
  if (state.input.type) {
    switch (state.input.type) {
      case "html":
        console.log("根据 type 字段路由到 htmlToCode");
        return "htmlToCode";
      case "img":
        console.log("根据 type 字段路由到 imgToCode");
        return "imgToCode";
      default:
        console.log("未知的 type 类型，尝试内容检测");
        break;
    }
  }

  // 如果没有 type 字段或类型未知，回退到内容检测
  const content = state.input.pageContent || state.input.pageName || "";

  if (!content) {
    console.log("没有输入内容，抛出错误");
    state.error = "输入验证失败：没有提供有效的内容";
    throw new Error(state.error);
  }

  console.log("分析输入内容类型:", content.substring(0, 100) + "...");

  // 检查是否为HTML内容
  if (isHtml(content)) {
    console.log("检测到HTML内容，路由到htmlToCode");
    return "htmlToCode";
  }

  // 检查是否为图片内容（图片URL、base64、图片描述等）
  if (isImg(content)) {
    console.log("检测到图片内容，路由到imgToCode");
    return "imgToCode";
  }

  // 如果都不是，抛出错误
  console.log("无法识别的内容类型，抛出错误");
  state.error = "输入验证失败：无法识别的内容类型";
  throw new Error(state.error);
}

// The chatbot node will call the LLM
async function htmlToCode(state: DesignToHtmlState) {
  const model = createModel();

  // Add system message to encourage tool usage
  const systemMessage = new SystemMessage(
    getHtmlToCodeSystemPrompt(state.input.pageName, state.input.pageContent)
  );

  // Add user message with the input content
  const userMessage = new HumanMessage(getHtmlRefactorPrompt());

  const messages = [systemMessage, userMessage];
  console.log(
    `Calling model with ${messages.length} messages and ${tools.length} tools`
  );

  printLog(messages);

  const response = await model.invoke(messages);
  console.log(`Model response tool_calls:`, response.tool_calls?.length || 0);

  return { messages: [...messages, response] };
}

async function imgToCode(state: DesignToHtmlState) {
  const model = createModel();

  // Add system message to encourage tool usage
  const systemMessage = new SystemMessage(getImgToCodeSystemPrompt());

  // Add user message with the input content
  const userMessage = new HumanMessage(
    state.input.pageContent || state.input.pageName || ""
  );

  const messages = [systemMessage, userMessage];
  console.log(
    `Calling model with ${messages.length} messages and ${tools.length} tools`
  );

  printLog(messages);
  const response = await model.invoke(messages);
  console.log(`Model response tool_calls:`, response.tool_calls?.length || 0);

  return { messages: [response] };
}

// 工具调用后递增轮次的函数
async function incrementRoundAfterTools(state: DesignToHtmlState) {
  const newRound = state.reviewRound + 1;
  console.log(`工具执行完成，轮次从 ${state.reviewRound} 递增到 ${newRound}`);

  return {
    reviewRound: newRound,
  };
}

// 决定是否继续审查的条件边函数
function shouldContinueReview(
  state: DesignToHtmlState
): "reviewCode" | typeof END {
  // 检查是否已达到最大轮次
  if (state.reviewRound >= MAX_REVIEW_ROUNDS) {
    console.log(
      `已完成 ${MAX_REVIEW_ROUNDS} 轮审查，达到最大轮次，结束审查流程`
    );
    printLog(state.messages);
    return END;
  }

  console.log(`准备进行第 ${state.reviewRound + 1} 轮代码审查`);
  return "reviewCode";
}

// The conditional edge will decide whether to call tools or end
function shouldCallTools(state: DesignToHtmlState): "tools" | "incrementRound" {
  const lastMessage = state.messages[state.messages.length - 1];

  // 检查是否有消息
  if (!lastMessage) {
    console.log(`没有消息，继续到轮次检查`);
    return "incrementRound";
  }

  // 只检查是否需要调用工具，不检查轮次
  if (lastMessage instanceof AIMessage && lastMessage.tool_calls?.length) {
    console.log(`第 ${state.reviewRound + 1} 轮需要调用工具，准备执行工具`);
    return "tools";
  }

  console.log(`第 ${state.reviewRound + 1} 轮无需调用工具，继续到轮次检查`);
  return "incrementRound";
}

// 代码审查节点
async function reviewCode(state: DesignToHtmlState) {
  const model = createModel().bindTools(tools);

  // 显示当前轮次（用于文案显示）
  const displayRound = state.reviewRound + 1;

  // 在现有消息列表后面添加审查追问，而不是重新设置system prompt
  const reviewMessage = [
    new HumanMessage(getReviewCodeSystemPrompt()),
    new HumanMessage(
      `请进行第${displayRound}轮代码审查。${getRoundControlPrompt(displayRound, MAX_REVIEW_ROUNDS)}`
    ),
  ];

  const messages = [...state.messages, ...reviewMessage];
  console.log(
    `Calling reviewCode model with ${messages.length} messages and ${tools.length} tools (Round ${displayRound}/${MAX_REVIEW_ROUNDS})`
  );

  printLog(messages);
  const response = await model.invoke(messages);
  console.log(
    `ReviewCode Model response tool_calls:`,
    response.tool_calls?.length || 0
  );

  return {
    messages: [...reviewMessage, response],
  };
}

const toolNode = new ToolNode(tools as any);

// We define the graph
const workflow = new StateGraph<DesignToHtmlState>({
  channels: DesignToHtmlStateObj,
})
  .addNode("htmlToCode", htmlToCode)
  .addNode("imgToCode", imgToCode)
  .addNode("reviewCode", reviewCode)
  .addNode("tools", toolNode)
  .addNode("incrementRound", incrementRoundAfterTools)
  .addConditionalEdges(START, routeByContentType, {
    htmlToCode: "htmlToCode",
    imgToCode: "imgToCode",
  })
  .addEdge("htmlToCode", "reviewCode")
  .addEdge("imgToCode", "reviewCode")
  .addConditionalEdges("reviewCode", shouldCallTools, {
    tools: "tools",
    incrementRound: "incrementRound",
  })
  .addEdge("tools", "incrementRound")
  .addConditionalEdges("incrementRound", shouldContinueReview, {
    [END]: END,
    reviewCode: "reviewCode",
  });

// And compile it
export const graph = workflow.compile({ checkpointer });
graph.name = "designToHtml";
