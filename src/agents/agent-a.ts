import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { StateGraph, START, END } from '@langchain/langgraph';
import { config, defaultAgentConfigs } from '../config/index.js';
import { NodeExecutionResult, AgentError } from '../types/index.js';
import { logger, PerformanceLogger } from '../utils/logger.js';
import { getAgentAPrompt } from './prompts.js';

// 定义Agent A的状态接口
interface AgentAState {
  input: string;
  output?: string;
  session_id: string;
  error?: string;
  metadata?: {
    execution_time?: number;
    tokens_used?: number;
    model_used?: string;
  };
}

export class AgentA {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private config: any;
  private workflow: any; // 简化的图工作流

  constructor() {
    this.config = defaultAgentConfigs.agent_a;

    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 60000,
    });

    // 创建提示词模板
    this.promptTemplate = getAgentAPrompt(this.config.systemPrompt);

    // 初始化LangGraph工作流
    this.initializeWorkflow();

    logger.info('Agent A initialized with LangGraph', {
      model: this.config.model,
      temperature: this.config.temperature
    });
  }

  // 初始化LangGraph工作流
  private initializeWorkflow(): void {
    try {
      // 根据LangGraph 0.0.34版本的API，使用channels包装
      const stateConfig = {
        channels: {
          input: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          output: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          session_id: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          error: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          metadata: {
            value: (x: any, y: any) => ({ ...x, ...y }),
            default: () => ({}),
          },
          // TODO: total_tokens
        }
      };

      // 创建StateGraph - 使用类型断言绕过复杂的类型检查
      this.workflow = new StateGraph(stateConfig as any)
        .addNode("analyze", this.analyzeNode.bind(this) as any)
        .addEdge(START, "analyze")
        .addEdge("analyze", END)
        .compile();

      logger.debug('LangGraph workflow initialized for Agent A');
    } catch (error: any) {
      logger.error('Failed to initialize LangGraph workflow', { error: error.message });
      throw new AgentError('Workflow initialization failed', 'WORKFLOW_INIT_FAILED', 'agent_a');
    }
  }

  // LangGraph节点函数：分析节点
  private async analyzeNode(state: AgentAState): Promise<Partial<AgentAState>> {
    const perfLogger = new PerformanceLogger(`agent_a_analyze_${state.session_id}`);

    try {
      logger.debug('Agent A analyze node starting', {
        session_id: state.session_id,
        input_length: state.input.length
      });

      // 验证输入
      if (!state.input || state.input.trim().length === 0) {
        throw new AgentError('Input cannot be empty', 'INVALID_INPUT', 'agent_a');
      }

      // 构建提示词
      const prompt = await this.promptTemplate.format({ input: state.input });

      perfLogger.checkpoint('prompt_formatted');

      // 调用模型
      const response = await this.model.invoke(prompt);

      perfLogger.checkpoint('model_invoked');

      // 处理响应
      const result = this.processResponse(response.content);

      const executionTime = perfLogger.end({
        input_length: state.input.length,
        output_length: result.length,
        success: true
      });

      logger.info('Agent A analyze node completed', {
        session_id: state.session_id,
        execution_time: executionTime,
        input_length: state.input.length,
        output_length: result.length
      });

      return {
        output: result,
        metadata: {
          execution_time: executionTime,
          tokens_used: this.estimateTokens(state.input, result),
          model_used: this.config.model
        }
      };

    } catch (error: any) {
      const executionTime = perfLogger.end({
        success: false,
        error: error.message
      });

      logger.error('Agent A analyze node failed', {
        session_id: state.session_id,
        error: error.message,
        execution_time: executionTime,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        return { error: error.message };
      }

      return {
        error: `Agent A execution failed: ${error.message}`,
        metadata: {
          execution_time: executionTime,
          model_used: this.config.model
        }
      };
    }
  }

  async execute(input: string, sessionId: string): Promise<NodeExecutionResult> {
    try {
      // 使用LangGraph StateGraph执行
      const state: AgentAState = {
        input,
        session_id: sessionId
      };

      const result = await this.workflow.invoke(state);

      if (result.error) {
        throw new AgentError(result.error, 'WORKFLOW_EXECUTION_FAILED', 'agent_a');
      }

      return {
        success: true,
        result: result.output || '',
        execution_time: result.metadata?.execution_time || 0,
        tokens_used: result.metadata?.tokens_used || 0
      };

    } catch (error: any) {
      logger.error('Agent A LangGraph execution failed', {
        session_id: sessionId,
        error: error.message,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent A execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_a',
        { originalError: error.message }
      );
    }
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      // 处理可能的结构化响应
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private estimateTokens(input: string, output: string): number {
    // 简单的token估算：大约4个字符=1个token
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testInput = "健康检查测试";
      const result = await this.execute(testInput, 'health-check-session');
      return result.success && result.result !== null;
    } catch (error: any) {
      logger.warn('Agent A health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens
    };
  }
}
