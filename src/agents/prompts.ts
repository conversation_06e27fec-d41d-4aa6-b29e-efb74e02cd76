import { PromptTemplate } from '@langchain/core/prompts';

export function getAgentAPrompt(systemPrompt: string) {
    return PromptTemplate.fromTemplate(`
${systemPrompt}

用户输入: {input}

请仔细分析用户的输入，提供深入的分析和见解。你的回答将作为后续智能体的重要输入，请确保内容准确、详细且有价值。

输出要求：
1. 对输入内容进行深入分析
2. 提取关键信息点
3. 提供专业见解
4. 保持回答的逻辑性和条理性

分析结果：
`);
}

export function getAgentBPromt(systemPrompt: string) {
    return PromptTemplate.fromTemplate(`
${systemPrompt}

用户输入: {input}

这是你的初次分析。请提供全面的回答，但要考虑到你可能需要进行后续的迭代优化。

分析要求：
1. 提供初步的深入分析
2. 识别可能需要进一步探讨的方面
3. 给出明确的结论和建议
4. 为后续迭代留下改进空间

当前迭代: 第1次
初步分析：
`)
}

export function getAgentBIterationPromt(systemPrompt: string) {
    return PromptTemplate.fromTemplate(`
${systemPrompt}

原始用户输入: {input}
当前迭代次数: 第{iteration}次
上一次的分析结果: {previous_result}

基于上一次的分析，请进行进一步的思考和优化。考虑以下方面：
1. 是否有遗漏的重要观点？
2. 分析的深度是否足够？
3. 结论是否需要调整？
4. 是否有新的见解可以补充？

优化后的分析：
`)
}

export function getAgentDPrompt(systemPrompt: string) {
    return PromptTemplate.fromTemplate(`
${systemPrompt}

你现在需要整合以下来自不同智能体的分析结果：

=== 原始用户输入 ===
{original_input}

=== Agent A 的分析结果 ===
{agent_a_result}

=== Agent B 的迭代分析结果 ===
{agent_b_result}

=== Agent C 的函数处理结果 ===
{agent_c_result}

=== 整合任务 ===
请基于以上所有信息，提供一个完整、准确、有价值的最终回答。你的整合需要：

1. **信息综合**: 将所有智能体的输出进行有机整合
2. **逻辑梳理**: 确保最终答案逻辑清晰、条理分明
3. **价值提取**: 突出最有价值的见解和结论
4. **完整性检查**: 确保回答完整地解决了用户的原始问题
5. **质量保证**: 提供高质量、专业的最终答案

=== 最终整合分析 ===
`);
}