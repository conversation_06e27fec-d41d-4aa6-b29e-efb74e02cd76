import { config, defaultAgentConfigs } from '../config/index.js';
import { NodeExecutionResult, AgentError } from '../types/index.js';
import { logger, PerformanceLogger } from '../utils/logger.js';

export class AgentC {
  private config: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_c;
    
    logger.info('Agent C initialized as function agent', { 
      name: this.config.name,
      description: this.config.description 
    });
  }

  async execute(codeA: string, sessionId: string): Promise<NodeExecutionResult> {
    const perfLogger = new PerformanceLogger(`agent_c_execution_${sessionId}`);
    
    try {
      logger.debug('Agent C starting execution', { 
        session_id: sessionId,
        code_a_length: codeA ? codeA.length : 0 
      });

      // 验证输入
      if (!codeA || codeA.trim().length === 0) {
        throw new AgentError('CodeA input cannot be empty', 'INVALID_INPUT', 'agent_c');
      }

      perfLogger.checkpoint('input_validated');

      // 执行核心函数：codeA + "xxxxdx"
      const result = this.executeFunction(codeA);
      
      perfLogger.checkpoint('function_executed');

      // 执行后处理和验证
      const processedResult = this.postProcessResult(result, codeA);
      
      const executionTime = perfLogger.end({
        input_length: codeA.length,
        output_length: processedResult.length,
        success: true
      });

      logger.info('Agent C execution completed', {
        session_id: sessionId,
        execution_time: executionTime,
        input_length: codeA.length,
        output_length: processedResult.length
      });

      return {
        success: true,
        result: processedResult,
        execution_time: executionTime,
        tokens_used: 0 // 函数执行不消耗LLM tokens
      };

    } catch (error: any) {
      const executionTime = perfLogger.end({
        success: false,
        error: error.message
      });

      logger.error('Agent C execution failed', {
        session_id: sessionId,
        error: error.message,
        execution_time: executionTime,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent C execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_c',
        { originalError: error.message }
      );
    }
  }

  private executeFunction(codeA: string): string {
    try {
      // 根据设计文档执行函数：codeA + "xxxxdx"
      const suffix = "xxxxdx";
      const result = codeA + suffix;
      
      logger.debug('Function executed', {
        input: codeA.substring(0, 50) + (codeA.length > 50 ? '...' : ''),
        suffix: suffix,
        result_preview: result.substring(0, 100) + (result.length > 100 ? '...' : '')
      });

      return result;
    } catch (error: any) {
      throw new AgentError(
        'Function execution failed',
        'FUNCTION_FAILED',
        'agent_c',
        { error: error.message }
      );
    }
  }

  private postProcessResult(result: string, originalInput: string): string {
    try {
      // 对结果进行后处理，添加执行信息
      const timestamp = new Date().toISOString();
      const metadata = {
        original_input_length: originalInput.length,
        suffix_added: "xxxxdx",
        execution_timestamp: timestamp,
        result_length: result.length
      };

      const processedResult = `=== Agent C 函数执行结果 ===

原始输入长度: ${metadata.original_input_length} 字符
添加后缀: "${metadata.suffix_added}"
执行时间: ${metadata.execution_timestamp}

函数执行结果:
${result}

执行详情:
- 函数类型: 字符串连接函数
- 操作: codeA + "xxxxdx"
- 结果长度: ${metadata.result_length} 字符
- 状态: 执行成功

=== 函数执行完成 ===`;

      return processedResult;
    } catch (error: any) {
      // 如果后处理失败，返回原始结果
      logger.warn('Post-processing failed, returning raw result', { error: error.message });
      return result;
    }
  }

  // 验证函数执行结果
  private validateResult(result: string, originalInput: string): boolean {
    try {
      // 基本验证：结果应该包含原始输入和后缀
      const expectedSuffix = "xxxxdx";
      
      if (!result.includes(originalInput)) {
        logger.warn('Result validation failed: original input not found in result');
        return false;
      }

      if (!result.includes(expectedSuffix)) {
        logger.warn('Result validation failed: expected suffix not found in result');
        return false;
      }

      return true;
    } catch (error: any) {
      logger.warn('Result validation error', { error: error.message });
      return false;
    }
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      const testInput = "健康检查";
      const result = await this.execute(testInput, 'health-check-session');
      
      // 验证健康检查结果
      const expectedOutput = testInput + "xxxxdx";
      const isHealthy = result.success && result.result.includes(expectedOutput);
      
      logger.debug('Agent C health check completed', { 
        success: isHealthy,
        test_input: testInput 
      });
      
      return isHealthy;
    } catch (error: any) {
      logger.warn('Agent C health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      type: 'function',
      operation: 'codeA + "xxxxdx"',
      model: this.config.model,
      supports_llm: false,
      supports_function: true
    };
  }

  // 获取函数执行统计
  getExecutionStats(): any {
    return {
      function_type: 'string_concatenation',
      operation: 'input + "xxxxdx"',
      average_execution_time: '<1ms',
      supports_streaming: false,
      memory_usage: 'minimal'
    };
  }

  // 执行干运行（不实际执行，只返回预期结果）
  async dryRun(codeA: string): Promise<{ expected_result: string; estimated_time: number }> {
    try {
      const expectedResult = codeA + "xxxxdx";
      const estimatedTime = 1; // 预估1毫秒执行时间
      
      return {
        expected_result: expectedResult,
        estimated_time: estimatedTime
      };
    } catch (error: any) {
      throw new AgentError(
        `Dry run failed: ${error.message}`,
        'DRY_RUN_FAILED',
        'agent_c',
        { originalError: error.message }
      );
    }
  }
}
