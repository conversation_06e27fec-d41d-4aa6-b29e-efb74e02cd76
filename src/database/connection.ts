import { MongoClient, Db, Collection } from 'mongodb';
import { config } from '../config/index.js';
import { ConversationHistory, AgentExecutionRecord } from '../types/index.js';
import { logger } from '../utils/logger.js';

export class DatabaseConnection {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private static instance: DatabaseConnection;

  private constructor() {}

  static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  async connect(): Promise<void> {
    try {
      this.client = new MongoClient(config.mongodb.uri, {
        maxPoolSize: 50,
        minPoolSize: 5,
        maxIdleTimeMS: 30000,
        serverSelectionTimeoutMS: 5000,
      });

      await this.client.connect();
      this.db = this.client.db(config.mongodb.dbName);
      
      // 创建索引
      await this.createIndexes();
      
      logger.info('MongoDB connected successfully', {
        dbName: config.mongodb.dbName,
        uri: config.mongodb.uri.replace(/\/\/.*@/, '//***@') // 隐藏密码
      });
    } catch (error) {
      logger.error('Failed to connect to MongoDB', { error });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      logger.info('MongoDB disconnected');
    }
  }

  getDb(): Db {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  getConversationsCollection(): Collection<ConversationHistory> {
    return this.getDb().collection<ConversationHistory>('conversations');
  }

  getAgentExecutionsCollection(): Collection<AgentExecutionRecord> {
    return this.getDb().collection<AgentExecutionRecord>('agent_executions');
  }

  private async createIndexes(): Promise<void> {
    try {
      const conversationsCollection = this.getConversationsCollection();
      const executionsCollection = this.getAgentExecutionsCollection();

      // 会话索引
      await conversationsCollection.createIndex({ session_id: 1 }, { unique: true });
      await conversationsCollection.createIndex({ user_id: 1, created_at: -1 });
      await conversationsCollection.createIndex({ 'messages.timestamp': -1 });
      await conversationsCollection.createIndex({ created_at: -1 });

      // 执行记录索引
      await executionsCollection.createIndex({ session_id: 1, execution_start: -1 });
      await executionsCollection.createIndex({ node_id: 1, execution_start: -1 });
      await executionsCollection.createIndex({ 'performance_metrics.latency_ms': 1 });
      await executionsCollection.createIndex({ execution_start: -1 });

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.warn('Failed to create some indexes', { error });
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) return false;
      await this.db.admin().ping();
      return true;
    } catch (error) {
      logger.error('Database health check failed', { error });
      return false;
    }
  }
}
