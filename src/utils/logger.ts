import winston from 'winston';
import { config } from '../config/index.js';
import fs from 'fs';
import path from 'path';

// 确保日志目录存在
const logDir = config.logging.filePath;
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义格式化器
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta
    });
  })
);

// 创建日志器
export const logger = winston.createLogger({
  level: config.logging.level,
  format: customFormat,
  defaultMeta: { 
    service: 'multi-agent-system',
    version: '1.0.0'
  },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
        winston.format.printf((info) => {
          const { timestamp, level, message, ...meta } = info;
          const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
          return `${timestamp} [${level}]: ${message}${metaStr}`;
        })
      )
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'app.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    })
  ],
  
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log')
    })
  ],
  
  // 处理未处理的 Promise 拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log')
    })
  ]
});

// 开发环境下的额外配置
if (config.server.nodeEnv === 'development') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// 导出日志方法
export const logInfo = (message: string, meta?: any) => logger.info(message, meta);
export const logError = (message: string, meta?: any) => logger.error(message, meta);
export const logWarn = (message: string, meta?: any) => logger.warn(message, meta);
export const logDebug = (message: string, meta?: any) => logger.debug(message, meta);

// 性能日志记录器
export class PerformanceLogger {
  private startTime: number;
  private operation: string;

  constructor(operation: string) {
    this.operation = operation;
    this.startTime = Date.now();
    logger.debug(`Started operation: ${operation}`);
  }

  end(additionalInfo?: any): number {
    const duration = Date.now() - this.startTime;
    logger.info(`Completed operation: ${this.operation}`, {
      duration_ms: duration,
      ...additionalInfo
    });
    return duration;
  }

  checkpoint(checkpointName: string, additionalInfo?: any): number {
    const currentDuration = Date.now() - this.startTime;
    logger.debug(`Checkpoint ${checkpointName} in operation: ${this.operation}`, {
      duration_ms: currentDuration,
      ...additionalInfo
    });
    return currentDuration;
  }
}

// 智能体执行日志记录器
export class AgentLogger {
  static logExecution(
    nodeId: string,
    sessionId: string,
    phase: 'start' | 'end' | 'error',
    data?: any
  ): void {
    const logData = {
      node_id: nodeId,
      session_id: sessionId,
      phase,
      timestamp: new Date().toISOString(),
      ...data
    };

    switch (phase) {
      case 'start':
        logger.info(`Agent execution started`, logData);
        break;
      case 'end':
        logger.info(`Agent execution completed`, logData);
        break;
      case 'error':
        logger.error(`Agent execution failed`, logData);
        break;
    }
  }

  static logStateTransition(
    sessionId: string,
    fromState: string,
    toState: string,
    metadata?: any
  ): void {
    logger.info('State transition', {
      session_id: sessionId,
      from_state: fromState,
      to_state: toState,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }
}
