// 系统配置类型
export interface SystemConfig {
  openai: {
    apiKey: string;
    apiBase?: string;
    modelName?: string;
  };
  mongodb: {
    uri: string;
    dbName: string;
  };
  server: {
    port: number;
    nodeEnv: string;
  };
  logging: {
    level: string;
    filePath: string;
  };
}

// 智能体状态类型
export interface AgentState {
  input: string;
  output: string;
  nodeA_result?: string;
  nodeB_result?: string;
  nodeC_result?: string;
  nodeD_result?: string;
  iteration_count: number;
  session_id: string;
  user_id?: string;
  timestamp: Date;
  error?: string;
  metadata?: Record<string, any>;
}

// 智能体配置类型
export interface AgentConfig {
  name: string;
  description: string;
  model: string;
  temperature: number;
  maxTokens?: number;
  tools?: string[];
  capabilities?: string[];
  systemPrompt?: string;
}

// 沙箱配置类型
export interface SandboxConfig {
  nodeId: string;
  limits: ResourceLimits;
  security: SecurityPolicy;
}

export interface ResourceLimits {
  maxMemoryMB: number;
  maxExecutionTimeMs: number;
  maxConcurrentRequests: number;
}

export interface SecurityPolicy {
  allowedDomains: string[];
  forbiddenOperations: string[];
  dataEncryptionRequired: boolean;
}

// 消息类型
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  node_id?: string;
  metadata?: {
    model_used?: string;
    tokens_consumed?: number;
    processing_time?: number;
    confidence_score?: number;
  };
}

// 会话历史类型
export interface ConversationHistory {
  _id?: string;
  session_id: string;
  user_id?: string;
  created_at: Date;
  updated_at: Date;
  messages: Message[];
  state_snapshots: StateSnapshot[];
  final_result?: {
    success: boolean;
    output: string;
    error_message?: string;
    total_processing_time: number;
  };
}

// 状态快照类型
export interface StateSnapshot {
  node_id: string;
  state_data: Record<string, any>;
  timestamp: Date;
  checkpoint_id: string;
}

// 智能体执行记录类型
export interface AgentExecutionRecord {
  _id?: string;
  session_id: string;
  node_id: string;
  agent_type: string;
  execution_start: Date;
  execution_end?: Date;
  input_data: Record<string, any>;
  output_data?: Record<string, any>;
  model_config: {
    model_name: string;
    temperature: number;
    max_tokens?: number;
  };
  performance_metrics?: {
    latency_ms: number;
    tokens_used: number;
    cost_estimate?: number;
  };
  error_info?: {
    error_type: string;
    error_message: string;
    stack_trace?: string;
  };
}

// 节点执行结果类型
export interface NodeExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  execution_time: number;
  tokens_used?: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

// 错误类型
export class AgentError extends Error {
  public readonly code: string;
  public readonly nodeId?: string;
  public readonly details?: Record<string, any>;

  constructor(message: string, code: string, nodeId?: string, details?: Record<string, any>) {
    super(message);
    this.name = 'AgentError';
    this.code = code;
    this.nodeId = nodeId;
    this.details = details;
  }
}

export class FatalError extends AgentError {
  constructor(message: string, nodeId?: string, details?: Record<string, any>) {
    super(message, 'FATAL_ERROR', nodeId, details);
    this.name = 'FatalError';
  }
}
