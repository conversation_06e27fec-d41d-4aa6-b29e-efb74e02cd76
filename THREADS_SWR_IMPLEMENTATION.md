# 线程SWR Hooks实现总结

基于参考文档 `/Users/<USER>/projs/github/open-swe/.ht/31-useThreadSWR.md` 和代码文件 `/Users/<USER>/projs/github/open-swe/apps/web/src/hooks/useThreadsSWR.ts`，我在当前项目中实现了类似的线程管理hooks。

## 实现的文件结构

```
web/src/
├── hooks/
│   ├── useThreadsSWR.ts      # 基础线程SWR hook
│   ├── useInfiniteThreads.ts # 无限滚动线程hook
│   ├── index.ts              # 导出文件
│   └── README.md             # 使用文档
├── lib/
│   ├── swr-config.ts         # SWR配置常量
│   └── thread-utils.ts       # 线程工具函数
├── providers/
│   └── client.ts             # LangGraph客户端提供者
├── types/
│   └── thread.ts             # 线程相关类型定义
├── components/
│   ├── thread-list.tsx       # 线程列表组件
│   └── ui/
│       ├── badge.tsx         # Badge组件
│       └── tabs.tsx          # Tabs组件
└── app/
    ├── threads/
    │   └── page.tsx          # 线程管理演示页面
    └── test-threads/
        └── page.tsx          # 简单测试页面
```

## 核心功能实现

### 1. useThreadsSWR Hook

**主要特性：**
- ✅ 支持按助手ID（图ID）过滤线程
- ✅ 支持按用户ID过滤线程  
- ✅ 分页支持（limit, offset, sortBy, sortOrder）
- ✅ 自动刷新和重新验证配置
- ✅ 错误处理和重试机制
- ✅ SWR缓存和去重
- ✅ TypeScript类型安全

**与参考实现的对应关系：**
- `assistantId` → 对应参考中的 `assistantId`
- `currentUserId` → 对应参考中的 `currentInstallation` 概念
- `disableUserFiltering` → 对应参考中的 `disableOrgFiltering`
- 分页参数完全对应
- SWR配置选项完全对应

### 2. useInfiniteThreads Hook

**额外特性：**
- ✅ 无限滚动支持
- ✅ 分页加载
- ✅ 加载更多功能
- ✅ 重置到第一页
- ✅ 所有基础hook的功能

### 3. 类型系统

**适配当前项目：**
- `AnyGraphState` → 包含 `DesignToCodeState` 和 `SimpleChatState`
- `ThreadSortBy` → 支持项目中的排序字段
- `PaginationOptions` → 完整的分页选项
- `ThreadMetadata` → 线程元数据结构

### 4. 工具函数

**实用工具：**
- `threadsToMetadata()` → 转换线程为显示格式
- `formatThreadUpdatedAt()` → 中文时间格式化
- `getThreadDisplayTitle()` → 获取显示标题
- `filterThreadsByGraphId()` → 按图ID过滤
- `sortThreadsByUpdatedAt()` → 时间排序

### 5. 配置管理

**SWR配置：**
```typescript
export const THREAD_SWR_CONFIG = {
  refreshInterval: 30000,      // 30秒自动刷新
  revalidateOnFocus: true,     // 窗口聚焦重新验证
  revalidateOnReconnect: true, // 重连时重新验证
  errorRetryCount: 3,          // 错误重试3次
  errorRetryInterval: 5000,    // 5秒重试间隔
  dedupingInterval: 2000,      // 2秒去重间隔
};
```

## 使用示例

### 基础用法
```tsx
import { useThreadsSWR } from "@/hooks/useThreadsSWR";

function MyComponent() {
  const { threads, error, isLoading, mutate } = useThreadsSWR({
    assistantId: "designToCode",
    currentUserId: "user123",
    pagination: {
      limit: 20,
      sortBy: "updated_at",
      sortOrder: "desc"
    }
  });

  // 使用threads数据...
}
```

### 无限滚动用法
```tsx
import { useInfiniteThreads } from "@/hooks/useInfiniteThreads";

function InfiniteList() {
  const { threads, hasMore, loadMore, isLoading } = useInfiniteThreads({
    assistantId: "simpleChat",
    pageSize: 10
  });

  // 渲染无限滚动列表...
}
```

## 演示页面

### 1. `/threads` - 完整演示页面
- 展示基础列表和无限滚动两种模式
- 提供过滤器控制
- 实时状态显示

### 2. `/test-threads` - 简单测试页面  
- 快速测试hook功能
- 显示详细的hook状态
- 便于调试和验证

## 与原项目的差异

1. **用户过滤逻辑**：原项目使用GitHub安装过滤，本项目使用用户ID过滤
2. **图状态类型**：适配了当前项目的图状态（DesignToCode, SimpleChat）
3. **API客户端**：使用项目现有的LangGraph SDK客户端
4. **UI组件**：使用项目现有的UI组件库
5. **中文化**：所有文本和时间格式都进行了中文化

## 技术栈兼容性

- ✅ Next.js 15.2.3
- ✅ React 19.0.0  
- ✅ SWR 2.3.4
- ✅ @langchain/langgraph-sdk 0.0.95
- ✅ TypeScript 5.7.2
- ✅ Tailwind CSS 4.0.13
- ✅ Radix UI组件

## 下一步建议

1. **测试集成**：在实际环境中测试API连接
2. **性能优化**：根据实际数据量调整缓存策略
3. **错误处理**：完善错误边界和用户反馈
4. **功能扩展**：根据需求添加搜索、筛选等功能
5. **单元测试**：为hooks编写测试用例

这个实现完全遵循了参考项目的设计模式，同时适配了当前项目的技术栈和业务需求。
