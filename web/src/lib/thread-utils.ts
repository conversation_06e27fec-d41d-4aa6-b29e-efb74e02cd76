import { Thread } from "@langchain/langgraph-sdk";
import { AnyGraphState, ThreadMetadata, ExtendedThread } from "@/types/thread";

/**
 * 将原始线程转换为线程元数据对象
 * @param threads 原始线程数组
 * @returns 线程元数据数组
 */
export function threadsToMetadata<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[]
): ThreadMetadata[] {
  return threads.map((thread) => ({
    thread_id: thread.thread_id,
    graph_id: thread.metadata?.graph_id,
    created_at: thread.created_at,
    updated_at: thread.updated_at,
    status: thread.status,
    title: thread.metadata?.title || `Thread ${thread.thread_id.slice(0, 8)}`,
    description: thread.metadata?.description,
    user_id: thread.metadata?.user_id,
    ...thread.metadata,
  }));
}

/**
 * 根据线程ID查找线程
 * @param threads 线程数组
 * @param threadId 线程ID
 * @returns 找到的线程或undefined
 */
export function findThreadById<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[],
  threadId: string
): Thread<T> | undefined {
  return threads.find((thread) => thread.thread_id === threadId);
}

/**
 * 按更新时间排序线程
 * @param threads 线程数组
 * @param order 排序顺序
 * @returns 排序后的线程数组
 */
export function sortThreadsByUpdatedAt<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[],
  order: "asc" | "desc" = "desc"
): Thread<T>[] {
  return [...threads].sort((a, b) => {
    const dateA = new Date(a.updated_at).getTime();
    const dateB = new Date(b.updated_at).getTime();
    return order === "desc" ? dateB - dateA : dateA - dateB;
  });
}

/**
 * 按创建时间排序线程
 * @param threads 线程数组
 * @param order 排序顺序
 * @returns 排序后的线程数组
 */
export function sortThreadsByCreatedAt<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[],
  order: "asc" | "desc" = "desc"
): Thread<T>[] {
  return [...threads].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return order === "desc" ? dateB - dateA : dateA - dateB;
  });
}

/**
 * 过滤特定图ID的线程
 * @param threads 线程数组
 * @param graphId 图ID
 * @returns 过滤后的线程数组
 */
export function filterThreadsByGraphId<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[],
  graphId: string
): Thread<T>[] {
  return threads.filter((thread) => thread.metadata?.graph_id === graphId);
}

/**
 * 过滤特定用户的线程
 * @param threads 线程数组
 * @param userId 用户ID
 * @returns 过滤后的线程数组
 */
export function filterThreadsByUserId<T extends AnyGraphState = AnyGraphState>(
  threads: Thread<T>[],
  userId: string
): Thread<T>[] {
  return threads.filter((thread) => thread.metadata?.user_id === userId);
}

/**
 * 获取线程的显示标题
 * @param thread 线程对象
 * @returns 显示标题
 */
export function getThreadDisplayTitle<T extends AnyGraphState = AnyGraphState>(
  thread: Thread<T>
): string {
  return (
    thread.metadata?.title ||
    thread.metadata?.description ||
    `Thread ${thread.thread_id.slice(0, 8)}`
  );
}

/**
 * 检查线程是否为活跃状态
 * @param thread 线程对象
 * @returns 是否活跃
 */
export function isThreadActive<T extends AnyGraphState = AnyGraphState>(
  thread: Thread<T>
): boolean {
  return thread.status === "busy" || thread.status === "pending";
}

/**
 * 格式化线程的更新时间
 * @param thread 线程对象
 * @returns 格式化的时间字符串
 */
export function formatThreadUpdatedAt<T extends AnyGraphState = AnyGraphState>(
  thread: Thread<T>
): string {
  const date = new Date(thread.updated_at);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) {
    return "刚刚";
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString("zh-CN");
  }
}
