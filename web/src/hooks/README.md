# Threads SWR Hooks

这个目录包含了用于管理LangGraph线程的SWR hooks，提供了高效的数据获取、缓存和状态管理功能。

## 文件结构

```
hooks/
├── useThreadsSWR.ts      # 基础线程SWR hook
├── useInfiniteThreads.ts # 无限滚动线程hook
└── README.md            # 使用文档
```

## 主要功能

### useThreadsSWR

基础的线程数据获取hook，支持：

- ✅ 按助手ID（图ID）过滤线程
- ✅ 按用户ID过滤线程
- ✅ 分页支持
- ✅ 排序功能
- ✅ 自动刷新和重新验证
- ✅ 错误处理和重试
- ✅ 缓存和去重

### useInfiniteThreads

无限滚动线程hook，支持：

- ✅ 分页加载
- ✅ 无限滚动
- ✅ 所有useThreadsSWR的功能
- ✅ 加载更多数据
- ✅ 重置到第一页

## 使用示例

### 基础用法

```tsx
import { useThreadsSWR } from "@/hooks/useThreadsSWR";

function ThreadList() {
  const { threads, error, isLoading, mutate } = useThreadsSWR({
    assistantId: "designToCode", // 可选：过滤特定图的线程
    currentUserId: "user123",    // 可选：过滤特定用户的线程
    pagination: {
      limit: 20,
      sortBy: "updated_at",
      sortOrder: "desc"
    }
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      {threads.map(thread => (
        <div key={thread.thread_id}>
          {thread.thread_id}
        </div>
      ))}
    </div>
  );
}
```

### 无限滚动用法

```tsx
import { useInfiniteThreads } from "@/hooks/useInfiniteThreads";

function InfiniteThreadList() {
  const {
    threads,
    error,
    isLoading,
    hasMore,
    loadMore
  } = useInfiniteThreads({
    assistantId: "simpleChat",
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc"
  });

  return (
    <div>
      {threads.map(thread => (
        <div key={thread.thread_id}>
          {thread.thread_id}
        </div>
      ))}
      
      {hasMore && (
        <button onClick={loadMore} disabled={isLoading}>
          {isLoading ? "加载中..." : "加载更多"}
        </button>
      )}
    </div>
  );
}
```

### 高级用法

```tsx
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { threadsToMetadata, formatThreadUpdatedAt } from "@/lib/thread-utils";

function AdvancedThreadList() {
  const {
    threads,
    error,
    isLoading,
    isValidating,
    mutate,
    hasMore
  } = useThreadsSWR({
    assistantId: "designToCode",
    refreshInterval: 30000, // 30秒自动刷新
    revalidateOnFocus: true,
    pagination: {
      limit: 25,
      offset: 0,
      sortBy: "updated_at",
      sortOrder: "desc"
    }
  });

  // 转换为元数据格式
  const threadMetadata = threadsToMetadata(threads);

  const handleRefresh = () => {
    mutate(); // 手动刷新
  };

  return (
    <div>
      <button onClick={handleRefresh} disabled={isValidating}>
        {isValidating ? "刷新中..." : "刷新"}
      </button>
      
      {threadMetadata.map(thread => (
        <div key={thread.thread_id}>
          <h3>{thread.title}</h3>
          <p>更新时间: {formatThreadUpdatedAt(thread)}</p>
          <p>状态: {thread.status}</p>
        </div>
      ))}
    </div>
  );
}
```

## 配置选项

### useThreadsSWR 选项

```typescript
interface UseThreadsSWROptions {
  assistantId?: string;           // 助手ID（图ID）
  refreshInterval?: number;       // 刷新间隔（毫秒）
  revalidateOnFocus?: boolean;    // 窗口聚焦时重新验证
  revalidateOnReconnect?: boolean; // 重新连接时重新验证
  currentUserId?: string | null;   // 当前用户ID
  disableUserFiltering?: boolean;  // 禁用用户过滤
  pagination?: PaginationOptions;  // 分页选项
}
```

### useInfiniteThreads 选项

```typescript
interface UseInfiniteThreadsOptions {
  assistantId?: string;           // 助手ID（图ID）
  refreshInterval?: number;       // 刷新间隔（毫秒）
  revalidateOnFocus?: boolean;    // 窗口聚焦时重新验证
  revalidateOnReconnect?: boolean; // 重新连接时重新验证
  currentUserId?: string | null;   // 当前用户ID
  disableUserFiltering?: boolean;  // 禁用用户过滤
  pageSize?: number;              // 每页大小
  sortBy?: ThreadSortBy;          // 排序字段
  sortOrder?: SortOrder;          // 排序顺序
}
```

## 工具函数

在 `@/lib/thread-utils` 中提供了多个实用工具函数：

- `threadsToMetadata()` - 转换线程为元数据格式
- `findThreadById()` - 根据ID查找线程
- `sortThreadsByUpdatedAt()` - 按更新时间排序
- `filterThreadsByGraphId()` - 按图ID过滤
- `getThreadDisplayTitle()` - 获取显示标题
- `formatThreadUpdatedAt()` - 格式化更新时间

## 注意事项

1. 确保设置了正确的 `NEXT_PUBLIC_API_URL` 环境变量
2. 线程数据会自动缓存，避免重复请求
3. 支持错误重试和自动刷新
4. 使用TypeScript获得更好的类型安全
