"use client";

import React from "react";
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { threadsToMetadata, formatThreadUpdatedAt, getThreadDisplayTitle } from "@/lib/thread-utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, MessageSquare, Clock, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ThreadListProps {
  /**
   * 助手ID（图ID），用于过滤特定图的线程
   */
  assistantId?: string;
  /**
   * 当前用户ID
   */
  currentUserId?: string;
  /**
   * 每页显示的线程数量
   */
  limit?: number;
  /**
   * 点击线程时的回调
   */
  onThreadClick?: (threadId: string) => void;
  /**
   * 是否显示用户信息
   */
  showUserInfo?: boolean;
}

/**
 * 线程列表组件
 * 使用useThreadsSWR hooks获取和显示线程列表
 */
export function ThreadList({
  assistantId,
  currentUserId,
  limit = 10,
  onThreadClick,
  showUserInfo = false,
}: ThreadListProps) {
  const {
    threads,
    error,
    isLoading,
    isValidating,
    mutate,
    hasMore,
  } = useThreadsSWR({
    assistantId,
    currentUserId,
    pagination: {
      limit,
      offset: 0,
      sortBy: "updated_at",
      sortOrder: "desc",
    },
  });

  // 转换为元数据格式以便显示
  const threadMetadata = threadsToMetadata(threads);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载线程中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">加载线程失败: {error.message}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => mutate()}
              className="mt-2"
            >
              重试
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (threadMetadata.length === 0) {
    return (
      <div className="p-8 text-center">
        <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">暂无线程</h3>
        <p className="mt-2 text-gray-500">
          {assistantId ? "该助手还没有任何对话线程" : "还没有任何对话线程"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">
          线程列表 ({threadMetadata.length})
        </h2>
        <div className="flex items-center gap-2">
          {isValidating && (
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => mutate()}
            disabled={isValidating}
          >
            刷新
          </Button>
        </div>
      </div>

      <div className="space-y-3">
        {threadMetadata.map((thread) => (
          <Card
            key={thread.thread_id}
            className="cursor-pointer transition-colors hover:bg-gray-50"
            onClick={() => onThreadClick?.(thread.thread_id)}
          >
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <CardTitle className="text-sm font-medium">
                  {getThreadDisplayTitle({ 
                    thread_id: thread.thread_id, 
                    metadata: thread,
                    created_at: thread.created_at,
                    updated_at: thread.updated_at,
                    status: thread.status 
                  })}
                </CardTitle>
                <Badge variant={thread.status === "busy" ? "default" : "secondary"}>
                  {thread.status || "idle"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      {formatThreadUpdatedAt({
                        thread_id: thread.thread_id,
                        updated_at: thread.updated_at,
                        created_at: thread.created_at,
                        metadata: thread,
                        status: thread.status
                      })}
                    </span>
                  </div>
                  {showUserInfo && thread.user_id && (
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>{thread.user_id}</span>
                    </div>
                  )}
                </div>
                <span className="font-mono text-xs">
                  {thread.thread_id.slice(0, 8)}
                </span>
              </div>
              {thread.description && (
                <p className="mt-2 text-xs text-gray-600 line-clamp-2">
                  {thread.description}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {hasMore && (
        <div className="text-center">
          <Button variant="outline" size="sm">
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
}
