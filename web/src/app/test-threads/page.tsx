"use client";

import React, { useState } from "react";
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw } from "lucide-react";

/**
 * 简单的线程测试页面
 * 用于快速测试useThreadsSWR hook的功能
 */
export default function TestThreadsPage() {
  const [assistantId, setAssistantId] = useState("");
  const [userId, setUserId] = useState("");

  const {
    threads,
    error,
    isLoading,
    isValidating,
    mutate,
    hasMore,
  } = useThreadsSWR({
    assistantId: assistantId || undefined,
    currentUserId: userId || undefined,
    pagination: {
      limit: 10,
      sortBy: "updated_at",
      sortOrder: "desc",
    },
    refreshInterval: 0, // 禁用自动刷新，便于测试
  });

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">线程Hook测试</h1>
        <p className="text-gray-600">
          测试useThreadsSWR hook的基本功能
        </p>
      </div>

      {/* 控制面板 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                助手ID (可选)
              </label>
              <Input
                placeholder="例如: designToCode"
                value={assistantId}
                onChange={(e) => setAssistantId(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                用户ID (可选)
              </label>
              <Input
                placeholder="例如: user123"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => mutate()}
              disabled={isValidating}
              variant="outline"
              size="sm"
            >
              {isValidating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  刷新中...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  手动刷新
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 状态信息 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Hook状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-500">加载状态</p>
              <Badge variant={isLoading ? "default" : "secondary"}>
                {isLoading ? "加载中" : "已加载"}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-500">验证状态</p>
              <Badge variant={isValidating ? "default" : "secondary"}>
                {isValidating ? "验证中" : "已验证"}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-500">线程数量</p>
              <Badge variant="outline">
                {threads.length} 个
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-500">更多数据</p>
              <Badge variant={hasMore ? "default" : "secondary"}>
                {hasMore ? "有更多" : "无更多"}
              </Badge>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">
                错误: {error.message}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 线程列表 */}
      <Card>
        <CardHeader>
          <CardTitle>线程数据</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && threads.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>正在加载线程...</span>
            </div>
          ) : threads.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {error ? "加载失败" : "暂无线程数据"}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                尝试调整过滤条件或检查API连接
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {threads.map((thread, index) => (
                <Card key={thread.thread_id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">
                          线程 #{index + 1}
                        </h3>
                        <p className="text-xs text-gray-500 mt-1 font-mono">
                          ID: {thread.thread_id}
                        </p>
                        <div className="mt-2 space-y-1">
                          <p className="text-xs text-gray-600">
                            创建时间: {new Date(thread.created_at).toLocaleString("zh-CN")}
                          </p>
                          <p className="text-xs text-gray-600">
                            更新时间: {new Date(thread.updated_at).toLocaleString("zh-CN")}
                          </p>
                          {thread.metadata?.graph_id && (
                            <p className="text-xs text-gray-600">
                              图ID: {thread.metadata.graph_id}
                            </p>
                          )}
                          {thread.metadata?.user_id && (
                            <p className="text-xs text-gray-600">
                              用户ID: {thread.metadata.user_id}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col gap-2">
                        <Badge variant={thread.status === "busy" ? "default" : "secondary"}>
                          {thread.status || "idle"}
                        </Badge>
                        {thread.metadata && Object.keys(thread.metadata).length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            有元数据
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* 显示元数据 */}
                    {thread.metadata && Object.keys(thread.metadata).length > 0 && (
                      <details className="mt-3">
                        <summary className="text-xs text-gray-500 cursor-pointer">
                          查看元数据
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-auto">
                          {JSON.stringify(thread.metadata, null, 2)}
                        </pre>
                      </details>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
