"use client";

import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { ChatInterface } from "@/components/chat-interface";
import { ChatSidebar } from "@/components/chat-sidebar";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { DEFAULT_AGENT } from "@/common/constants";

export default function ChatPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const threadId = params.thread_id as string;
  const selectedAgent = searchParams.get('agent') || DEFAULT_AGENT; // 默认使用simpleChat
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 验证thread_id格式
  useEffect(() => {
    if (!threadId || typeof threadId !== 'string') {
      router.push('/');
      return;
    }
  }, [threadId, router]);

  const handleBack = () => {
    router.push('/');
  };

  if (!threadId) {
    return null;
  }

  return (
    <div className="h-screen flex bg-background">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* 侧边栏 */}
      <div className={`
        fixed lg:relative lg:translate-x-0 transition-transform duration-300 ease-in-out z-50
        w-80 h-full bg-background border-r
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex items-center justify-between p-4 border-b lg:hidden">
          <h2 className="font-semibold">聊天记录</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <ChatSidebar currentChatId={threadId} className="border-0" selectedAgent={selectedAgent} />
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 移动端顶部栏 */}
        <div className="lg:hidden flex items-center gap-2 p-4 border-b bg-background">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="font-semibold truncate">聊天 - {threadId.slice(0, 8)}...</h1>
          </div>
        </div>

        {/* 聊天界面 */}
        <div className="flex-1">
          <ChatInterface
            onBack={handleBack}
            threadId={threadId}
            selectedAgent={selectedAgent}
          />
        </div>
      </div>
    </div>
  );
}
