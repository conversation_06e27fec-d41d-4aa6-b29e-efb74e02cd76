"use client";

import React, { useState } from "react";
import { ThreadList } from "@/components/thread-list";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useInfiniteThreads } from "@/hooks/useInfiniteThreads";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from "lucide-react";

/**
 * 线程管理演示页面
 * 展示useThreadsSWR和useInfiniteThreads的使用方法
 */
export default function ThreadsPage() {
  const [selectedAssistant, setSelectedAssistant] = useState<string>("");
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const [pageSize, setPageSize] = useState<number>(10);

  // 使用无限滚动hook作为演示
  const {
    threads: infiniteThreads,
    error: infiniteError,
    isLoading: infiniteLoading,
    hasMore,
    loadMore,
    refresh,
    reset,
  } = useInfiniteThreads({
    assistantId: selectedAssistant || undefined,
    currentUserId: currentUserId || undefined,
    pageSize,
    sortBy: "updated_at",
    sortOrder: "desc",
  });

  const handleThreadClick = (threadId: string) => {
    console.log("点击线程:", threadId);
    // 这里可以导航到线程详情页面
    // router.push(`/chat/${threadId}`);
  };

  const assistantOptions = [
    { value: "", label: "所有助手" },
    { value: "designToCode", label: "设计转代码" },
    { value: "simpleChat", label: "简单聊天" },
  ];

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">线程管理</h1>
        <p className="text-gray-600">
          演示useThreadsSWR和useInfiniteThreads hooks的使用
        </p>
      </div>

      <Tabs defaultValue="basic" className="space-y-6">
        <TabsList>
          <TabsTrigger value="basic">基础列表</TabsTrigger>
          <TabsTrigger value="infinite">无限滚动</TabsTrigger>
        </TabsList>

        {/* 过滤器 */}
        <Card>
          <CardHeader>
            <CardTitle>过滤选项</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">助手类型</label>
                <Select value={selectedAssistant} onValueChange={setSelectedAssistant}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择助手" />
                  </SelectTrigger>
                  <SelectContent>
                    {assistantOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">用户ID</label>
                <Input
                  placeholder="输入用户ID"
                  value={currentUserId}
                  onChange={(e) => setCurrentUserId(e.target.value)}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">每页大小</label>
                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={refresh} variant="outline" size="sm">
                刷新数据
              </Button>
              <Button onClick={reset} variant="outline" size="sm">
                重置到第一页
              </Button>
            </div>
          </CardContent>
        </Card>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基础线程列表 (useThreadsSWR)</CardTitle>
            </CardHeader>
            <CardContent>
              <ThreadList
                assistantId={selectedAssistant || undefined}
                currentUserId={currentUserId || undefined}
                limit={pageSize}
                onThreadClick={handleThreadClick}
                showUserInfo={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="infinite" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>无限滚动线程列表 (useInfiniteThreads)</CardTitle>
                <Badge variant="secondary">
                  {infiniteThreads.length} 个线程
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {infiniteError && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600">加载失败: {infiniteError.message}</p>
                </div>
              )}

              <div className="space-y-3">
                {infiniteThreads.map((thread) => (
                  <Card
                    key={thread.thread_id}
                    className="cursor-pointer transition-colors hover:bg-gray-50"
                    onClick={() => handleThreadClick(thread.thread_id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-sm">
                            {thread.metadata?.title || `Thread ${thread.thread_id.slice(0, 8)}`}
                          </h3>
                          <p className="text-xs text-gray-500 mt-1">
                            ID: {thread.thread_id}
                          </p>
                          <p className="text-xs text-gray-500">
                            更新时间: {new Date(thread.updated_at).toLocaleString("zh-CN")}
                          </p>
                        </div>
                        <Badge variant={thread.status === "busy" ? "default" : "secondary"}>
                          {thread.status || "idle"}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {infiniteThreads.length === 0 && !infiniteLoading && (
                <div className="text-center py-8">
                  <p className="text-gray-500">暂无线程数据</p>
                </div>
              )}

              {hasMore && (
                <div className="text-center mt-6">
                  <Button
                    onClick={loadMore}
                    disabled={infiniteLoading}
                    variant="outline"
                  >
                    {infiniteLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        加载中...
                      </>
                    ) : (
                      "加载更多"
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
