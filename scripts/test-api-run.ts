import { Client } from "@langchain/langgraph-sdk";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { DesignItem } from "../src/graph/designToCode/types.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const html1 = fs.readFileSync(
  path.resolve(__dirname, "html", "index.1.html"),
  "utf-8"
);

const html2 = fs.readFileSync(
  path.resolve(__dirname, "html", "index.2.html"),
  "utf-8"
);
// 测试设计稿数据
const testDesignItems: DesignItem[] = [
  {
    pageName: "首页",
    pageContent: html1,
    type: "html",
  },
  {
    pageName: "产品页",
    pageContent: html2,
    type: "html",
  },
];

// fs.writeFileSync(
//   path.resolve(__dirname, '../web/src/common/mock.ts'),
//   `export const inputDesignItems = ${JSON.stringify({input: testDesignItems}, null, 2)}`
// );

export async function testApiRun() {
  const client = new Client({ apiUrl: "http://localhost:2024" });
  const thread = await client.threads.create();
  const assistantID = "designToCode";
  const input = { input: testDesignItems }; 
  let run = await client.runs.create(thread["thread_id"], assistantID, { input });
  
  // 运行
  await client.runs.join(thread["thread_id"], run["run_id"]); 

  console.log(run);
}

testApiRun().catch((error) => {
  console.error("❌ 测试运行失败:", error);
  process.exit(1);
});
