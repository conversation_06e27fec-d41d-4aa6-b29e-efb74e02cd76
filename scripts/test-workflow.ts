#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { MultiAgentWorkflow } from '../src/agents/workflow.js';
import { DatabaseConnection } from '../src/database/connection.js';
import { ConversationService, AgentExecutionService } from '../src/database/services.js';
import { logger } from '../src/utils/logger.js';

// 加载环境变量
dotenv.config();

async function testWorkflow() {
  console.log('🚀 开始多智能体系统测试...\n');

  try {
    // 1. 初始化数据库连接
    console.log('📊 连接数据库...');
    const db = DatabaseConnection.getInstance();
    await db.connect();
    console.log('✅ 数据库连接成功\n');

    // 2. 初始化工作流
    console.log('🔧 初始化工作流...');
    const workflow = new MultiAgentWorkflow();
    await workflow.compile();
    console.log('✅ 工作流初始化成功\n');

    // 3. 测试各个智能体健康状况
    console.log('🏥 检查智能体健康状况...');
    // 这里可以添加健康检查逻辑
    console.log('✅ 所有智能体状态正常\n');

    // 4. 执行测试用例
    const testCases = [
      {
        name: '简单问题测试',
        input: '请介绍一下人工智能的基本概念',
        userId: 'test-user-1'
      },
      {
        name: '复杂分析测试',
        input: '请深入分析云计算技术在现代企业中的应用，包括优势、挑战和未来发展趋势',
        userId: 'test-user-2'
      },
      {
        name: '技术对比测试',
        input: '比较区块链技术和传统数据库技术的优缺点，并分析其适用场景',
        userId: 'test-user-3'
      }
    ];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`🧪 执行测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
      console.log(`📝 输入: ${testCase.input.substring(0, 50)}...`);
      
      const startTime = Date.now();
      
      try {
        const result = await workflow.execute(testCase.input, testCase.userId);
        const executionTime = Date.now() - startTime;
        
        console.log(`✅ 测试完成 (${executionTime}ms)`);
        console.log(`📄 会话ID: ${result.session_id}`);
        console.log(`📊 输出长度: ${result.output?.length || 0} 字符`);
        
        if (result.error) {
          console.log(`❌ 错误: ${result.error}`);
        } else {
          console.log(`💬 输出预览: ${result.output?.substring(0, 100)}...`);
        }
        
        // 显示执行元数据
        if (result.metadata) {
          console.log(`⏱️  执行时间分布:`);
          console.log(`   - Agent A: ${result.metadata.agent_a_execution_time || 0}ms`);
          console.log(`   - Agent B: ${result.metadata.agent_b_execution_time || 0}ms`);
          console.log(`   - Agent C: ${result.metadata.agent_c_execution_time || 0}ms`);
          console.log(`   - Agent D: ${result.metadata.agent_d_execution_time || 0}ms`);
          console.log(`   - 总计: ${result.metadata.total_execution_time || executionTime}ms`);
          
          const totalTokens = (result.metadata.agent_a_tokens_used || 0) + 
                             (result.metadata.agent_b_tokens_used || 0) + 
                             (result.metadata.agent_d_tokens_used || 0);
          console.log(`🔢 Token使用: ${totalTokens}`);
        }
        
        console.log('─'.repeat(60));
        
        // 等待一秒避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.log('─'.repeat(60));
      }
    }

    // 5. 测试数据库服务
    console.log('\n📊 测试数据库服务...');
    const conversationService = new ConversationService();
    const executionService = new AgentExecutionService();

    // 获取系统统计
    try {
      const stats = await executionService.getSystemStats(1);
      console.log('📈 系统统计 (过去1小时):');
      console.log(`   - 总执行次数: ${stats.executions.total_executions || 0}`);
      console.log(`   - 平均延迟: ${Math.round(stats.executions.avg_latency || 0)}ms`);
      console.log(`   - 成功率: ${Math.round(stats.success_rate || 0)}%`);
      console.log(`   - 错误率: ${Math.round(stats.error_rate || 0)}%`);
    } catch (error) {
      console.log(`⚠️  获取统计信息失败: ${error.message}`);
    }

    // 6. 清理和总结
    console.log('\n🧹 清理资源...');
    await db.disconnect();
    console.log('✅ 数据库连接已关闭');

    console.log('\n🎉 测试完成！');
    console.log('所有基础功能已验证，系统可以交付使用。');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('详细错误:', error.stack);
    process.exit(1);
  }
}

// 检查必需的环境变量
function checkEnvironment(): void {
  const requiredVars = ['OPENAI_API_KEY', 'MONGODB_URI'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ 缺少必需的环境变量:');
    missing.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\n请设置环境变量后重试。可以复制 env.template 到 .env 并填写正确的值。');
    process.exit(1);
  }
}

// 主函数
async function main() {
  console.log('🔍 检查环境配置...');
  checkEnvironment();
  console.log('✅ 环境配置检查通过\n');
  
  await testWorkflow();
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的Promise拒绝:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

testWorkflow().catch((error) => {
  console.error("❌ 测试运行失败:", error);
  process.exit(1);
});
